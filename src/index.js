// <PERSON>flare Worker to fetch Bible passages from BibleGateway.com
// Optimized for Kindle Paperwhite 7th gen (WebKit 534, ES5 only)

// Display Configuration - Set to true/false to show/hide elements
var showVerseNumbers = false;      // <sup class="versenum">
var showCrossReferences = false;   // <sup class="crossreference">
var showFootnotes = false;         // <sup class="footnote">
var showSectionHeadings = false;   // <h3> section titles

var BIBLE_BOOKS = [
    { name: 'Genesis', chapters: 50 },
    { name: 'Exodus', chapters: 40 },
    { name: 'Leviticus', chapters: 27 },
    { name: 'Numbers', chapters: 36 },
    { name: 'Deuteronomy', chapters: 34 },
    { name: '<PERSON>', chapters: 24 },
    { name: '<PERSON>', chapters: 21 },
    { name: '<PERSON>', chapters: 4 },
    { name: '1 <PERSON>', chapters: 31 },
    { name: '2 <PERSON>', chapters: 24 },
    { name: '1 Kings', chapters: 22 },
    { name: '2 Kings', chapters: 25 },
    { name: '1 Chronicles', chapters: 29 },
    { name: '2 Chronicles', chapters: 36 },
    { name: '<PERSON>', chapters: 10 },
    { name: 'Nehemia<PERSON>', chapters: 13 },
    { name: '<PERSON>', chapters: 10 },
    { name: '<PERSON>', chapters: 42 },
    { name: 'Psalms', chapters: 150 },
    { name: 'Proverbs', chapters: 31 },
    { name: 'Ecclesiastes', chapters: 12 },
    { name: 'Song of Songs', chapters: 8 },
    { name: 'Isaiah', chapters: 66 },
    { name: '<PERSON>', chapters: 52 },
    { name: 'Lamentations', chapters: 5 },
    { name: 'Ezekiel', chapters: 48 },
    { name: 'Daniel', chapters: 12 },
    { name: 'Hosea', chapters: 14 },
    { name: 'Joel', chapters: 3 },
    { name: 'Amos', chapters: 9 },
    { name: 'Obadiah', chapters: 1 },
    { name: 'Jonah', chapters: 4 },
    { name: 'Micah', chapters: 7 },
    { name: 'Nahum', chapters: 3 },
    { name: 'Habakkuk', chapters: 3 },
    { name: 'Zephaniah', chapters: 3 },
    { name: 'Haggai', chapters: 2 },
    { name: 'Zechariah', chapters: 14 },
    { name: 'Malachi', chapters: 4 },
    { name: 'Matthew', chapters: 28 },
    { name: 'Mark', chapters: 16 },
    { name: 'Luke', chapters: 24 },
    { name: 'John', chapters: 21 },
    { name: 'Acts', chapters: 28 },
    { name: 'Romans', chapters: 16 },
    { name: '1 Corinthians', chapters: 16 },
    { name: '2 Corinthians', chapters: 13 },
    { name: 'Galatians', chapters: 6 },
    { name: 'Ephesians', chapters: 6 },
    { name: 'Philippians', chapters: 4 },
    { name: 'Colossians', chapters: 4 },
    { name: '1 Thessalonians', chapters: 5 },
    { name: '2 Thessalonians', chapters: 3 },
    { name: '1 Timothy', chapters: 6 },
    { name: '2 Timothy', chapters: 4 },
    { name: 'Titus', chapters: 3 },
    { name: 'Philemon', chapters: 1 },
    { name: 'Hebrews', chapters: 13 },
    { name: 'James', chapters: 5 },
    { name: '1 Peter', chapters: 5 },
    { name: '2 Peter', chapters: 3 },
    { name: '1 John', chapters: 5 },
    { name: '2 John', chapters: 1 },
    { name: '3 John', chapters: 1 },
    { name: 'Jude', chapters: 1 },
    { name: 'Revelation', chapters: 22 }
];

function parsePassageReference(passageRef) {
    var match = passageRef.match(/^(.+?)\s+(\d+)$/);
    if (match) {
        return {
            book: match[1].trim(),
            chapter: parseInt(match[2])
        };
    }
    return null;
}

function getNavigationUrls(passageRef, translation) {
    var parsed = parsePassageReference(passageRef);
    if (!parsed) return { prev: null, next: null };

    var currentBookIndex = -1;
    for (var i = 0; i < BIBLE_BOOKS.length; i++) {
        if (BIBLE_BOOKS[i].name.toLowerCase() === parsed.book.toLowerCase()) {
            currentBookIndex = i;
            break;
        }
    }
    if (currentBookIndex === -1) return { prev: null, next: null };

    var currentBook = BIBLE_BOOKS[currentBookIndex];
    var currentChapter = parsed.chapter;

    var prevUrl = null;
    var nextUrl = null;

    if (currentChapter > 1) {
        prevUrl = '/?search=' + encodeURIComponent(parsed.book + ' ' + (currentChapter - 1)) + '&version=' + translation;
    } else if (currentBookIndex > 0) {
        var prevBook = BIBLE_BOOKS[currentBookIndex - 1];
        prevUrl = '/?search=' + encodeURIComponent(prevBook.name + ' ' + prevBook.chapters) + '&version=' + translation;
    }

    if (currentChapter < currentBook.chapters) {
        nextUrl = '/?search=' + encodeURIComponent(parsed.book + ' ' + (currentChapter + 1)) + '&version=' + translation;
    } else if (currentBookIndex < BIBLE_BOOKS.length - 1) {
        var nextBook = BIBLE_BOOKS[currentBookIndex + 1];
        nextUrl = '/?search=' + encodeURIComponent(nextBook.name + ' 1') + '&version=' + translation;
    }

    return { prev: prevUrl, next: nextUrl };
}

function generateDisplayCSS() {
    var css = '';
    if (!showVerseNumbers) css += '        .versenum { display: none; }\n';
    if (!showCrossReferences) css += '        .crossreference { display: none; }\n';
    if (!showFootnotes) css += '        .footnote { display: none; }\n';
    if (!showSectionHeadings) css += '        h3 { display: none; }\n';
    css += '        .chapternum { display: none; }\n';
    css += '        .woj { color: inherit; font-weight: inherit; }\n';
    return css;
}



export default {
    async fetch(request) {
        try {
            var url = new URL(request.url);
            if (url.pathname === '/favicon.ico') {
                return new Response(null, { status: 204 });
            }

            var searchParam = url.searchParams.get('search');
            var versionParam = url.searchParams.get('version');

            if (!searchParam || !versionParam) {
                return new Response('Invalid URL. Use: /?search=John%205&version=NIV', { status: 400 });
            }

            var translation = versionParam.toUpperCase();
            var passageRef = decodeURIComponent(searchParam);
            var bibleGatewayUrl = 'https://www.biblegateway.com/passage/?search=' +
                encodeURIComponent(passageRef) + '&version=' + encodeURIComponent(translation);

            var response = await fetch(bibleGatewayUrl, {
                headers: { 'User-Agent': 'Mozilla/5.0 (X11; U; Linux armv7l like Android; en-us) AppleWebKit/531.2 (KHTML, like Gecko) Version/5.0 Safari/533.2 Kindle/3.0' }
            });

            if (!response.ok) {
                return new Response('Failed to fetch passage: ' + response.status, { status: 500 });
            }

            var htmlText = await response.text();
            var stdTextStart = htmlText.search(/<div[^>]*class=['"]std-text['"][^>]*>/);
            var contentContainer = '';
            var contentStart = -1;
            var contentEnd = -1;

            if (stdTextStart !== -1) {
                // Found std-text div - extract it completely with its opening tag
                var openTagMatch = htmlText.substring(stdTextStart).match(/^<div[^>]*class=['"]std-text['"][^>]*>/);
                if (!openTagMatch) {
                    return new Response('Could not parse std-text div tag.', { status: 404 });
                }
                contentStart = stdTextStart + openTagMatch[0].length;
                var divCount = 1;
                var pos = contentStart;
                while (pos < htmlText.length && divCount > 0) {
                    var nextDiv = htmlText.indexOf('<div', pos);
                    var nextCloseDiv = htmlText.indexOf('</div>', pos);
                    if (nextCloseDiv === -1) break;
                    if (nextDiv !== -1 && nextDiv < nextCloseDiv) {
                        divCount++;
                        pos = nextDiv + 4;
                    } else {
                        divCount--;
                        if (divCount === 0) {
                            contentEnd = nextCloseDiv;
                            break;
                        }
                        pos = nextCloseDiv + 6;
                    }
                }
                if (divCount > 0) {
                    return new Response('Could not find complete std-text content.', { status: 404 });
                }
                // Preserve the complete std-text div structure
                contentContainer = htmlText.substring(stdTextStart, contentEnd + 6);
            } else {
                // Fallback: look for version-specific div
                var versionDivPattern = new RegExp('<div[^>]*class="[^"]*version-' + translation + '[^"]*"[^>]*>');
                var versionDivStart = htmlText.search(versionDivPattern);
                if (versionDivStart === -1) {
                    return new Response('Could not find passage content.', { status: 404 });
                }
                var versionOpenTagMatch = htmlText.substring(versionDivStart).match(/^<div[^>]*>/);
                if (!versionOpenTagMatch) {
                    return new Response('Could not parse version div tag.', { status: 404 });
                }
                contentStart = versionDivStart + versionOpenTagMatch[0].length;
                var divCount = 1;
                var pos = contentStart;
                while (pos < htmlText.length && divCount > 0) {
                    var nextDiv = htmlText.indexOf('<div', pos);
                    var nextCloseDiv = htmlText.indexOf('</div>', pos);
                    if (nextCloseDiv === -1) break;
                    if (nextDiv !== -1 && nextDiv < nextCloseDiv) {
                        divCount++;
                        pos = nextDiv + 4;
                    } else {
                        divCount--;
                        if (divCount === 0) {
                            contentEnd = nextCloseDiv;
                            break;
                        }
                        pos = nextCloseDiv + 6;
                    }
                }
                if (divCount > 0) {
                    return new Response('Could not find complete version content.', { status: 404 });
                }
                var rawContent = htmlText.substring(contentStart, contentEnd);
                // Clean up unwanted elements while preserving structure
                rawContent = rawContent.replace(/<h1[^>]*class=['"][^'"]*passage-display[^'"]*['"][^>]*>[\s\S]*?<\/h1>/g, '');
                rawContent = rawContent.replace(/<div[^>]*class=['"][^'"]*dropdowns[^'"]*['"][^>]*>[\s\S]*?<\/div>/g, '');
                rawContent = rawContent.replace(/<div[^>]*class=['"][^'"]*publisher-info-bottom[^'"]*['"][^>]*>[\s\S]*?<\/div>/g, '');
                // Wrap in std-text div to maintain consistency
                contentContainer = '<div class="std-text">' + rawContent + '</div>';
            }

            // Validate that we have actual content
            if (!contentContainer || contentContainer.trim().length < 50) {
                return new Response('No valid content found in passage.', { status: 404 });
            }

            var titleMatch = htmlText.match(/<h1[^>]*class=['"][^'"]*bcv[^'"]*['"][^>]*>([\s\S]*?)<\/h1>/);
            var title = titleMatch ? titleMatch[1].replace(/<[^>]*>/g, '').trim() : '';
            var cleanTitle = title || passageRef;
            if (cleanTitle.indexOf(' - ') !== -1) {
                cleanTitle = cleanTitle.substring(0, cleanTitle.indexOf(' - '));
            }

            var navigation = getNavigationUrls(passageRef, translation);

            // Generate Kindle-optimized HTML using the proven working approach
            var html = '<!DOCTYPE html>\n' +
                '<html lang="en">\n' +
                '<head>\n' +
                '    <meta charset="UTF-8">\n' +
                '    <title>' + cleanTitle + '</title>\n' +
                '    <style>\n' +
                '        /* Kindle Paperwhite 7th gen optimizations */\n' +
                '        body {\n' +
                '            width: 100%; /* Required for clientWidth to work */\n' +
                '            font-family: serif;\n' +
                '            font-size: 1.8rem; /* Large text for e-ink readability */\n' +
                '            line-height: 1.7;\n' +
                '            margin: 0;\n' +
                '            padding: 2rem;\n' +
                '            background-color: white;\n' +
                '            color: black;\n' +
                '            /* Hide scrollbar using nested container trick */\n' +
                '            overflow: hidden;\n' +
                '        }\n' +
                '        .content-wrapper {\n' +
                '            width: 103%;\n' +
                '            height: 100vh;\n' +
                '            overflow-y: auto;\n' +
                '            padding-right: 3%;\n' +
                '            margin-right: -3%;\n' +
                '        }\n' +
                '        .inner-content {\n' +
                '            max-width: 1000px;\n' +
                '            margin: 0 auto;\n' +
                '        }\n' +
                '        h1 {\n' +
                '            font-size: 2.2rem;\n' +
                '            text-align: left;\n' +
                '            margin-bottom: 2rem;\n' +
                '            font-weight: normal;\n' +
                '        }\n' +
                '        /* Preserve BibleGateway styling but optimize for Kindle */\n' +
                '        .std-text {\n' +
                '            font-size: inherit;\n' +
                '            line-height: inherit;\n' +
                '        }\n' +
                '        .std-text p {\n' +
                '            margin-bottom: 1.2rem;\n' +
                '        }\n' +
                '        /* Style verse numbers if present */\n' +
                '        .versenum {\n' +
                '            font-size: 0.8rem;\n' +
                '            vertical-align: super;\n' +
                '            color: #666;\n' +
                '        }\n' +
                '        /* Style cross-references and footnotes if present */\n' +
                '        .crossreference, .footnote {\n' +
                '            font-size: 0.7rem;\n' +
                '            vertical-align: super;\n' +
                '            color: #999;\n' +
                '        }\n' +
                '        /* Remove link underlines using data-href workaround */\n' +
                '        a {\n' +
                '            color: inherit;\n' +
                '            text-decoration: none;\n' +
                '        }\n' +
                '        /* Float-based layout for compatibility */\n' +
                '        .float-left { float: left; }\n' +
                '        .float-right { float: right; }\n' +
                '        .clearfix:after {\n' +
                '            content: "";\n' +
                '            display: table;\n' +
                '            clear: both;\n' +
                '        }\n' +
                '        .footer {\n' +
                '            margin-top: 3rem;\n' +
                '            text-align: center;\n' +
                '            font-size: 1rem;\n' +
                '            color: #666;\n' +
                '            clear: both;\n' +
                '        }\n' +
                '        .navigation {\n' +
                '            margin-top: 2rem;\n' +
                '            text-align: center;\n' +
                '            clear: both;\n' +
                '        }\n' +
                '        .nav-link {\n' +
                '            display: inline-block;\n' +
                '            margin: 0 1rem;\n' +
                '            padding: 0.5rem 1rem;\n' +
                '            background-color: #f5f5f5;\n' +
                '            color: #333;\n' +
                '            text-decoration: none;\n' +
                '            border-radius: 3px;\n' +
                '        }\n' +
                '        .nav-link.disabled {\n' +
                '            color: #ccc;\n' +
                '            background-color: #fafafa;\n' +
                '        }\n' +
                '        /* Dynamic display configuration */\n' +
                generateDisplayCSS() +
                '    </style>\n' +
                '</head>\n' +
                '<body>\n' +
                '    <div class="content-wrapper">\n' +
                '        <div class="inner-content">\n' +
                '            <h1>' + cleanTitle + '</h1>\n' +
                '            ' + contentContainer + '\n' +
                '            <div class="navigation">\n' +
                (navigation.prev ? '                <a href="' + navigation.prev + '" class="nav-link">&lt; Previous</a>\n' : '                <span class="nav-link disabled">&lt; Previous</span>\n') +
                (navigation.next ? '                <a href="' + navigation.next + '" class="nav-link">Next &gt;</a>\n' : '                <span class="nav-link disabled">Next &gt;</span>\n') +
                '            </div>\n' +
                '            <div class="footer">\n' +
                '                <p>' + translation.toUpperCase() + ' - BibleGateway.com</p>\n' +
                '            </div>\n' +
                '        </div>\n' +
                '    </div>\n' +
                '</body>\n' +
                '</html>';

            return new Response(html, {
                headers: { 'Content-Type': 'text/html; charset=utf-8' }
            });

        } catch (error) {
            return new Response('Internal server error: ' + error.message, { status: 500 });
        }
    }
};